#!/usr/bin/env python3
"""
Script d'analyse et validation des données financières
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datasets import load_from_disk
from collections import Counter
import re
from wordcloud import WordCloud
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DataAnalyzer:
    def __init__(self, dataset_path: str = "./preprocessed_financial_dataset"):
        """
        Initialise l'analyseur avec le dataset prétraité
        """
        try:
            self.dataset = load_from_disk(dataset_path)
            logger.info(f"Dataset chargé depuis: {dataset_path}")
        except Exception as e:
            logger.error(f"Erreur lors du chargement: {e}")
            self.dataset = None
    
    def analyze_text_quality(self):
        """
        Analyse de la qualité des textes
        """
        if self.dataset is None:
            return
        
        print("=== ANALYSE DE LA QUALITÉ DES TEXTES ===\n")
        
        for split in self.dataset:
            print(f"\n--- {split.upper()} ---")
            data = self.dataset[split]
            
            # Conversion pour analyse
            df = data.to_pandas()
            
            # Statistiques de longueur
            if 'input_ids' in df.columns:
                lengths = df['input_ids'].apply(lambda x: len([token for token in x if token != 0]))
                print(f"Longueur des séquences (tokens):")
                print(f"  Min: {lengths.min()}")
                print(f"  Max: {lengths.max()}")
                print(f"  Moyenne: {lengths.mean():.2f}")
                print(f"  Médiane: {lengths.median():.2f}")
                print(f"  Std: {lengths.std():.2f}")
                
                # Distribution des longueurs
                plt.figure(figsize=(10, 6))
                plt.hist(lengths, bins=50, alpha=0.7, edgecolor='black')
                plt.title(f'Distribution des longueurs de séquences - {split}')
                plt.xlabel('Nombre de tokens')
                plt.ylabel('Fréquence')
                plt.axvline(lengths.mean(), color='red', linestyle='--', label=f'Moyenne: {lengths.mean():.1f}')
                plt.legend()
                plt.tight_layout()
                plt.savefig(f'length_distribution_{split}.png', dpi=300, bbox_inches='tight')
                plt.show()
    
    def analyze_label_distribution(self):
        """
        Analyse de la distribution des labels
        """
        if self.dataset is None:
            return
        
        print("\n=== DISTRIBUTION DES LABELS ===\n")
        
        label_names = {0: 'Neutre', 1: 'Positif', 2: 'Négatif'}
        
        fig, axes = plt.subplots(1, len(self.dataset), figsize=(15, 5))
        if len(self.dataset) == 1:
            axes = [axes]
        
        for idx, split in enumerate(self.dataset):
            data = self.dataset[split]
            df = data.to_pandas()
            
            if 'label' in df.columns:
                label_counts = df['label'].value_counts().sort_index()
                
                print(f"--- {split.upper()} ---")
                total = len(df)
                for label, count in label_counts.items():
                    percentage = (count / total) * 100
                    label_name = label_names.get(label, f'Label_{label}')
                    print(f"  {label_name}: {count} ({percentage:.1f}%)")
                
                # Graphique
                ax = axes[idx] if len(self.dataset) > 1 else axes[0]
                labels = [label_names.get(i, f'Label_{i}') for i in label_counts.index]
                colors = ['gray', 'green', 'red']
                
                bars = ax.bar(labels, label_counts.values, color=colors[:len(labels)])
                ax.set_title(f'Distribution - {split}')
                ax.set_ylabel('Nombre d\'échantillons')
                
                # Ajout des pourcentages sur les barres
                for bar, count in zip(bars, label_counts.values):
                    height = bar.get_height()
                    percentage = (count / total) * 100
                    ax.text(bar.get_x() + bar.get_width()/2., height + total*0.01,
                           f'{percentage:.1f}%', ha='center', va='bottom')
        
        plt.tight_layout()
        plt.savefig('label_distribution.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def detect_data_issues(self):
        """
        Détection des problèmes potentiels dans les données
        """
        if self.dataset is None:
            return
        
        print("\n=== DÉTECTION DES PROBLÈMES ===\n")
        
        for split in self.dataset:
            print(f"--- {split.upper()} ---")
            data = self.dataset[split]
            df = data.to_pandas()
            
            issues = []
            
            # Vérification des séquences vides
            if 'input_ids' in df.columns:
                empty_sequences = df['input_ids'].apply(
                    lambda x: all(token in [0, 101, 102] for token in x)  # CLS, SEP, PAD tokens
                ).sum()
                if empty_sequences > 0:
                    issues.append(f"Séquences quasi-vides: {empty_sequences}")
            
            # Vérification des labels manquants
            if 'label' in df.columns:
                missing_labels = df['label'].isna().sum()
                if missing_labels > 0:
                    issues.append(f"Labels manquants: {missing_labels}")
                
                # Labels hors plage attendue
                invalid_labels = df[~df['label'].isin([0, 1, 2])]['label'].count()
                if invalid_labels > 0:
                    issues.append(f"Labels invalides: {invalid_labels}")
            
            # Doublons potentiels (basé sur les input_ids)
            if 'input_ids' in df.columns:
                input_ids_str = df['input_ids'].apply(lambda x: str(x[:50]))  # Premier 50 tokens
                duplicates = input_ids_str.duplicated().sum()
                if duplicates > 0:
                    issues.append(f"Doublons potentiels: {duplicates}")
            
            if issues:
                for issue in issues:
                    print(f"  ⚠️  {issue}")
            else:
                print("  ✅ Aucun problème détecté")
    
    def sample_analysis(self, num_samples: int = 5):
        """
        Analyse d'échantillons aléatoires
        """
        if self.dataset is None:
            return
        
        print(f"\n=== ANALYSE D'ÉCHANTILLONS ({num_samples} par split) ===\n")
        
        from transformers import AutoTokenizer
        tokenizer = AutoTokenizer.from_pretrained("ProsusAI/finbert")
        label_names = {0: 'Neutre', 1: 'Positif', 2: 'Négatif'}
        
        for split in self.dataset:
            print(f"--- {split.upper()} ---")
            data = self.dataset[split]
            
            # Échantillonnage aléatoire
            indices = np.random.choice(len(data), min(num_samples, len(data)), replace=False)
            
            for i, idx in enumerate(indices):
                sample = data[idx]
                
                # Décodage du texte
                input_ids = sample['input_ids']
                decoded_text = tokenizer.decode(input_ids, skip_special_tokens=True)
                label = sample['label']
                label_name = label_names.get(label, f'Label_{label}')
                
                print(f"\nÉchantillon {i+1}:")
                print(f"  Label: {label_name} ({label})")
                print(f"  Longueur: {len([token for token in input_ids if token != 0])} tokens")
                print(f"  Texte: {decoded_text[:200]}{'...' if len(decoded_text) > 200 else ''}")
                print("-" * 50)
    
    def generate_report(self):
        """
        Génère un rapport complet d'analyse
        """
        print("🔍 RAPPORT D'ANALYSE DES DONNÉES FINANCIÈRES")
        print("=" * 60)
        
        self.analyze_text_quality()
        self.analyze_label_distribution()
        self.detect_data_issues()
        self.sample_analysis()
        
        print("\n" + "=" * 60)
        print("✅ Analyse terminée. Vérifiez les graphiques générés.")

def main():
    """
    Fonction principale d'analyse
    """
    analyzer = DataAnalyzer()
    analyzer.generate_report()

if __name__ == "__main__":
    main()
