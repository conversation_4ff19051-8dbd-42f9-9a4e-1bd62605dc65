#!/usr/bin/env python3
"""
Script de prétraitement optimisé pour l'analyse de sentiment financier
Dataset: <PERSON><PERSON><PERSON>/financial_news_sentiment_mixte_with_phrasebank_75
"""

import pandas as pd
import numpy as np
import re
from datasets import load_dataset, Dataset, DatasetDict
from transformers import AutoTokenizer
from sklearn.model_selection import train_test_split
import html
import unicodedata
from typing import Dict, List, Any
import logging

# Configuration du logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class FinancialDataPreprocessor:
    def __init__(self, model_name: str = "ProsusAI/finbert", max_length: int = 512):
        """
        Initialise le préprocesseur avec le tokenizer FinBERT
        """
        self.tokenizer = AutoTokenizer.from_pretrained(model_name)
        self.max_length = max_length
        logger.info(f"Tokenizer chargé: {model_name}")
    
    def clean_text(self, text: str) -> str:
        """
        Nettoyage avancé du texte pour les données financières
        """
        if pd.isna(text) or text is None:
            return ""
        
        # Conversion en string si nécessaire
        text = str(text)
        
        # Décodage HTML
        text = html.unescape(text)
        
        # Normalisation Unicode
        text = unicodedata.normalize('NFKD', text)
        
        # Suppression des balises HTML/XML
        text = re.sub(r'<[^>]+>', ' ', text)
        
        # Nettoyage des caractères spéciaux financiers
        # Préservation des symboles monétaires et pourcentages
        text = re.sub(r'&[a-zA-Z]+;', ' ', text)  # Entités HTML
        text = re.sub(r'[^\w\s\$€£¥%\.,\-\+\(\)]', ' ', text)  # Garde les symboles financiers
        
        # Nettoyage des espaces multiples
        text = re.sub(r'\s+', ' ', text)
        
        # Suppression des espaces en début/fin
        text = text.strip()
        
        return text
    
    def validate_data(self, dataset) -> Dict[str, Any]:
        """
        Validation et statistiques du dataset
        """
        stats = {}
        
        # Conversion en DataFrame pour l'analyse
        df = dataset.to_pandas()
        
        # Statistiques générales
        stats['total_samples'] = len(df)
        stats['columns'] = list(df.columns)
        
        # Vérification des valeurs nulles
        null_counts = df.isnull().sum()
        stats['null_values'] = null_counts.to_dict()
        
        # Statistiques sur les textes
        if 'summary_detail_with_title' in df.columns:
            text_lengths = df['summary_detail_with_title'].str.len()
            stats['text_length'] = {
                'min': text_lengths.min(),
                'max': text_lengths.max(),
                'mean': text_lengths.mean(),
                'median': text_lengths.median()
            }
        
        # Distribution des labels
        if 'labels' in df.columns:
            label_dist = df['labels'].value_counts().to_dict()
            stats['label_distribution'] = label_dist
        
        return stats
    
    def preprocess_dataset(self, dataset_name: str = "Jean-Baptiste/financial_news_sentiment_mixte_with_phrasebank_75"):
        """
        Prétraitement complet du dataset
        """
        logger.info(f"Chargement du dataset: {dataset_name}")
        
        # 1. Chargement du dataset
        try:
            dataset = load_dataset(dataset_name)
            logger.info("Dataset chargé avec succès")
        except Exception as e:
            logger.error(f"Erreur lors du chargement: {e}")
            return None
        
        # 2. Vérification de la structure
        logger.info("Structure du dataset:")
        print(dataset)
        
        # 3. Validation initiale
        if 'train' in dataset:
            train_data = dataset['train']
        else:
            train_data = dataset['train'] if 'train' in dataset else dataset[list(dataset.keys())[0]]
        
        initial_stats = self.validate_data(train_data)
        logger.info(f"Statistiques initiales: {initial_stats}")
        
        # 4. Nettoyage des données
        def clean_examples(examples):
            cleaned_examples = {}
            
            # Nettoyage du texte principal
            if 'summary_detail_with_title' in examples:
                cleaned_examples['summary_detail_with_title'] = [
                    self.clean_text(text) for text in examples['summary_detail_with_title']
                ]
            
            # Nettoyage des autres champs textuels
            for field in ['summary_detail', 'title']:
                if field in examples:
                    cleaned_examples[field] = [
                        self.clean_text(text) for text in examples[field]
                    ]
            
            # Préservation des autres champs
            for key, value in examples.items():
                if key not in cleaned_examples:
                    cleaned_examples[key] = value
            
            return cleaned_examples
        
        # Application du nettoyage
        logger.info("Nettoyage des données en cours...")
        if 'train' in dataset:
            cleaned_train = dataset['train'].map(clean_examples, batched=True)
            cleaned_dataset = DatasetDict({'train': cleaned_train})
            
            if 'test' in dataset:
                cleaned_test = dataset['test'].map(clean_examples, batched=True)
                cleaned_dataset['test'] = cleaned_test
        else:
            cleaned_data = train_data.map(clean_examples, batched=True)
            cleaned_dataset = DatasetDict({'train': cleaned_data})
        
        # 5. Filtrage des échantillons vides ou trop courts
        def filter_valid_samples(example):
            text = example.get('summary_detail_with_title', '')
            return len(text.strip()) >= 10  # Minimum 10 caractères
        
        logger.info("Filtrage des échantillons invalides...")
        for split in cleaned_dataset:
            original_size = len(cleaned_dataset[split])
            cleaned_dataset[split] = cleaned_dataset[split].filter(filter_valid_samples)
            new_size = len(cleaned_dataset[split])
            logger.info(f"Split {split}: {original_size} -> {new_size} échantillons")
        
        # 6. Division train/validation/test si nécessaire
        if 'test' not in cleaned_dataset:
            logger.info("Création des splits train/validation/test...")
            train_val = cleaned_dataset['train'].train_test_split(test_size=0.3, seed=42)
            val_test = train_val['test'].train_test_split(test_size=0.5, seed=42)
            
            cleaned_dataset = DatasetDict({
                'train': train_val['train'],
                'validation': val_test['train'],
                'test': val_test['test']
            })
        elif 'validation' not in cleaned_dataset:
            logger.info("Création du split de validation...")
            train_val = cleaned_dataset['train'].train_test_split(test_size=0.2, seed=42)
            cleaned_dataset['train'] = train_val['train']
            cleaned_dataset['validation'] = train_val['test']
        
        return cleaned_dataset
    
    def tokenize_dataset(self, dataset):
        """
        Tokenisation du dataset avec FinBERT
        """
        def tokenize_function(examples):
            # Tokenisation avec padding et truncation
            tokenized = self.tokenizer(
                examples['summary_detail_with_title'],
                padding='max_length',
                truncation=True,
                max_length=self.max_length,
                return_tensors=None
            )
            
            # Renommage des labels si nécessaire
            if 'labels' in examples:
                tokenized['label'] = examples['labels']
            
            return tokenized
        
        logger.info("Tokenisation en cours...")
        tokenized_dataset = dataset.map(
            tokenize_function,
            batched=True,
            remove_columns=dataset['train'].column_names
        )
        
        # Configuration pour PyTorch
        tokenized_dataset.set_format(
            "torch",
            columns=["input_ids", "attention_mask", "label"]
        )
        
        return tokenized_dataset
    
    def get_dataset_statistics(self, dataset):
        """
        Statistiques finales du dataset prétraité
        """
        stats = {}
        
        for split in dataset:
            split_data = dataset[split]
            stats[split] = {
                'size': len(split_data),
                'features': list(split_data.features.keys())
            }
            
            # Distribution des labels
            if 'label' in split_data.features:
                labels = split_data['label']
                unique, counts = np.unique(labels, return_counts=True)
                stats[split]['label_distribution'] = dict(zip(unique.tolist(), counts.tolist()))
        
        return stats

def main():
    """
    Fonction principale pour exécuter le prétraitement
    """
    # Initialisation du préprocesseur
    preprocessor = FinancialDataPreprocessor()
    
    # Prétraitement du dataset
    cleaned_dataset = preprocessor.preprocess_dataset()
    
    if cleaned_dataset is None:
        logger.error("Échec du prétraitement")
        return
    
    # Tokenisation
    tokenized_dataset = preprocessor.tokenize_dataset(cleaned_dataset)
    
    # Statistiques finales
    final_stats = preprocessor.get_dataset_statistics(tokenized_dataset)
    logger.info("Statistiques finales:")
    for split, stats in final_stats.items():
        print(f"\n{split.upper()}:")
        print(f"  Taille: {stats['size']}")
        print(f"  Distribution des labels: {stats.get('label_distribution', 'N/A')}")
    
    # Sauvegarde du dataset prétraité
    logger.info("Sauvegarde du dataset prétraité...")
    tokenized_dataset.save_to_disk("./preprocessed_financial_dataset")
    
    logger.info("Prétraitement terminé avec succès!")
    return tokenized_dataset

if __name__ == "__main__":
    dataset = main()
