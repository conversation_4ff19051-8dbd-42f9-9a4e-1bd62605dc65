#!/usr/bin/env python3
"""
Script de prétraitement rapide et optimisé pour l'analyse de sentiment financier
Commandes essentielles pour un nettoyage efficace
"""

from datasets import load_dataset
from transformers import AutoTokenizer
import pandas as pd
import numpy as np
import re
import html

def quick_clean_and_tokenize():
    """
    Prétraitement rapide en une seule fonction
    """
    print("🚀 Démarrage du prétraitement rapide...")
    
    # 1. CHARGEMENT RAPIDE
    print("📥 Chargement du dataset...")
    dataset = load_dataset("<PERSON><PERSON><PERSON>/financial_news_sentiment_mixte_with_phrasebank_75")
    print(f"✅ Dataset chargé: {len(dataset['train'])} échantillons")
    
    # 2. NETTOYAGE EXPRESS
    def clean_text_fast(text):
        """Nettoyage rapide et efficace"""
        if not text or pd.isna(text):
            return ""
        
        text = str(text)
        text = html.unescape(text)  # Décodage HTML
        text = re.sub(r'<[^>]+>', ' ', text)  # Suppression balises HTML
        text = re.sub(r'[^\w\s\$€£¥%\.,\-\+\(\)]', ' ', text)  # Caractères spéciaux
        text = re.sub(r'\s+', ' ', text).strip()  # Espaces multiples
        
        return text
    
    def preprocess_batch(examples):
        """Prétraitement par batch pour optimiser les performances"""
        # Nettoyage du texte principal
        cleaned_texts = [clean_text_fast(text) for text in examples['summary_detail_with_title']]
        
        return {
            'summary_detail_with_title': cleaned_texts,
            'labels': examples['labels']
        }
    
    print("🧹 Nettoyage des données...")
    cleaned_dataset = dataset.map(
        preprocess_batch,
        batched=True,
        remove_columns=[col for col in dataset['train'].column_names 
                       if col not in ['summary_detail_with_title', 'labels']]
    )
    
    # 3. FILTRAGE RAPIDE
    print("🔍 Filtrage des échantillons invalides...")
    def is_valid(example):
        return len(example['summary_detail_with_title'].strip()) >= 10
    
    filtered_dataset = cleaned_dataset.filter(is_valid)
    print(f"✅ Après filtrage: {len(filtered_dataset['train'])} échantillons")
    
    # 4. TOKENISATION OPTIMISÉE
    print("🔤 Tokenisation avec FinBERT...")
    tokenizer = AutoTokenizer.from_pretrained("ProsusAI/finbert")
    
    def tokenize_fast(examples):
        return tokenizer(
            examples['summary_detail_with_title'],
            padding='max_length',
            truncation=True,
            max_length=512,
            return_tensors=None
        )
    
    tokenized_dataset = filtered_dataset.map(
        tokenize_fast,
        batched=True,
        remove_columns=['summary_detail_with_title']
    )
    
    # Renommage des labels
    tokenized_dataset = tokenized_dataset.rename_column('labels', 'label')
    
    # 5. DIVISION TRAIN/VAL/TEST
    print("📊 Création des splits...")
    if 'test' not in tokenized_dataset:
        # Split train (70%) / temp (30%)
        train_temp = tokenized_dataset['train'].train_test_split(test_size=0.3, seed=42)
        # Split temp en validation (15%) et test (15%)
        val_test = train_temp['test'].train_test_split(test_size=0.5, seed=42)
        
        final_dataset = {
            'train': train_temp['train'],
            'validation': val_test['train'],
            'test': val_test['test']
        }
    else:
        # Si test existe déjà, créer seulement validation
        train_val = tokenized_dataset['train'].train_test_split(test_size=0.2, seed=42)
        final_dataset = {
            'train': train_val['train'],
            'validation': train_val['test'],
            'test': tokenized_dataset['test']
        }
    
    # 6. FORMAT PYTORCH
    print("🔧 Configuration pour PyTorch...")
    from datasets import DatasetDict
    final_dataset = DatasetDict(final_dataset)
    
    final_dataset.set_format("torch", columns=["input_ids", "attention_mask", "label"])
    
    # 7. STATISTIQUES RAPIDES
    print("\n📈 STATISTIQUES FINALES:")
    print("-" * 40)
    
    label_names = {0: 'Neutre', 1: 'Positif', 2: 'Négatif'}
    
    for split_name, split_data in final_dataset.items():
        print(f"\n{split_name.upper()}:")
        print(f"  Taille: {len(split_data)}")
        
        # Distribution des labels
        labels = split_data['label'].numpy()
        unique, counts = np.unique(labels, return_counts=True)
        
        print("  Distribution des labels:")
        for label, count in zip(unique, counts):
            percentage = (count / len(labels)) * 100
            label_name = label_names.get(label, f'Label_{label}')
            print(f"    {label_name}: {count} ({percentage:.1f}%)")
    
    # 8. SAUVEGARDE
    print("\n💾 Sauvegarde du dataset prétraité...")
    final_dataset.save_to_disk("./financial_dataset_processed")
    print("✅ Dataset sauvegardé dans './financial_dataset_processed'")
    
    return final_dataset

def validate_preprocessing():
    """
    Validation rapide du prétraitement
    """
    print("\n🔍 VALIDATION DU PRÉTRAITEMENT")
    print("-" * 40)
    
    try:
        from datasets import load_from_disk
        dataset = load_from_disk("./financial_dataset_processed")
        
        # Vérifications essentielles
        checks = []
        
        # 1. Structure des données
        expected_columns = ['input_ids', 'attention_mask', 'label']
        for split in dataset:
            missing_cols = [col for col in expected_columns if col not in dataset[split].column_names]
            if missing_cols:
                checks.append(f"❌ Colonnes manquantes dans {split}: {missing_cols}")
            else:
                checks.append(f"✅ Structure correcte pour {split}")
        
        # 2. Plage des labels
        for split in dataset:
            labels = dataset[split]['label']
            unique_labels = set(labels)
            if unique_labels.issubset({0, 1, 2}):
                checks.append(f"✅ Labels valides dans {split}: {unique_labels}")
            else:
                checks.append(f"❌ Labels invalides dans {split}: {unique_labels}")
        
        # 3. Longueur des séquences
        for split in dataset:
            input_ids = dataset[split]['input_ids']
            lengths = [len(seq) for seq in input_ids]
            if all(length == 512 for length in lengths):
                checks.append(f"✅ Longueur uniforme dans {split}: 512 tokens")
            else:
                checks.append(f"❌ Longueurs variables dans {split}")
        
        # Affichage des résultats
        for check in checks:
            print(check)
        
        # Échantillon de test
        print(f"\n📝 ÉCHANTILLON DE TEST:")
        sample = dataset['train'][0]
        print(f"  Input IDs shape: {len(sample['input_ids'])}")
        print(f"  Attention mask shape: {len(sample['attention_mask'])}")
        print(f"  Label: {sample['label']}")
        
        # Décodage d'un échantillon
        from transformers import AutoTokenizer
        tokenizer = AutoTokenizer.from_pretrained("ProsusAI/finbert")
        decoded = tokenizer.decode(sample['input_ids'], skip_special_tokens=True)
        print(f"  Texte décodé: {decoded[:100]}...")
        
        print("\n✅ Validation terminée avec succès!")
        
    except Exception as e:
        print(f"❌ Erreur lors de la validation: {e}")

def main():
    """
    Exécution complète du prétraitement
    """
    print("🎯 PRÉTRAITEMENT OPTIMISÉ POUR SENTIMENT FINANCIER")
    print("=" * 60)
    
    # Prétraitement
    dataset = quick_clean_and_tokenize()
    
    # Validation
    validate_preprocessing()
    
    print("\n🎉 PRÉTRAITEMENT TERMINÉ!")
    print("Le dataset est prêt pour l'entraînement du modèle FinBERT.")
    print("Fichiers générés:")
    print("  - ./financial_dataset_processed/ (dataset tokenisé)")
    
    return dataset

if __name__ == "__main__":
    dataset = main()
