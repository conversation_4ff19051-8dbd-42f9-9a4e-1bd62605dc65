# Script PowerShell pour automatiser le prétraitement des données financières
# Usage: .\run_preprocessing.ps1

Write-Host "🚀 DÉMARRAGE DU PRÉTRAITEMENT DES DONNÉES FINANCIÈRES" -ForegroundColor Green
Write-Host "=" * 60

# Vérification de Python
Write-Host "🔍 Vérification de l'environnement..." -ForegroundColor Yellow
try {
    $pythonVersion = python --version 2>&1
    Write-Host "✅ Python détecté: $pythonVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Python non trouvé. Veuillez installer Python 3.8+" -ForegroundColor Red
    exit 1
}

# Installation des dépendances
Write-Host "`n📦 Installation des dépendances..." -ForegroundColor Yellow
try {
    pip install -r requirements.txt
    Write-Host "✅ Dépendances installées avec succès" -ForegroundColor Green
} catch {
    Write-Host "❌ Erreur lors de l'installation des dépendances" -ForegroundColor Red
    exit 1
}

# Exécution du prétraitement rapide
Write-Host "`n🔄 Exécution du prétraitement..." -ForegroundColor Yellow
try {
    python quick_preprocessing.py
    Write-Host "✅ Prétraitement terminé avec succès" -ForegroundColor Green
} catch {
    Write-Host "❌ Erreur lors du prétraitement" -ForegroundColor Red
    exit 1
}

# Analyse des données (optionnel)
$analysis = Read-Host "`n❓ Voulez-vous exécuter l'analyse des données? (y/N)"
if ($analysis -eq "y" -or $analysis -eq "Y") {
    Write-Host "`n📊 Exécution de l'analyse..." -ForegroundColor Yellow
    try {
        python data_analysis.py
        Write-Host "✅ Analyse terminée" -ForegroundColor Green
    } catch {
        Write-Host "❌ Erreur lors de l'analyse" -ForegroundColor Red
    }
}

Write-Host "`n🎉 PROCESSUS TERMINÉ!" -ForegroundColor Green
Write-Host "Les données sont prêtes pour l'entraînement du modèle." -ForegroundColor Cyan
Write-Host "Dataset prétraité disponible dans: ./financial_dataset_processed/" -ForegroundColor Cyan
