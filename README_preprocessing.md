# 🔧 Prétraitement des Données Financières pour FinBERT

Ce repository contient des scripts optimisés pour le prétraitement du dataset `<PERSON><PERSON><PERSON>/financial_news_sentiment_mixte_with_phrasebank_75` en vue de l'entraînement d'un modèle FinBERT pour l'analyse de sentiment financier.

## 📋 Prérequis

- Python 3.8+
- pip ou pip3
- Au moins 4GB de RAM libre
- Connexion internet pour télécharger le dataset

## 🚀 Installation et Exécution Rapide

### Option 1: Script Automatisé (Recommandé)

**Windows (PowerShell):**
```powershell
.\run_preprocessing.ps1
```

**Linux/Mac (Bash):**
```bash
chmod +x run_preprocessing.sh
./run_preprocessing.sh
```

### Option 2: Exécution Manuelle

1. **Installation des dépendances:**
```bash
pip install -r requirements.txt
```

2. **Prétraitement rapide:**
```bash
python quick_preprocessing.py
```

3. **Analyse des données (optionnel):**
```bash
python data_analysis.py
```

## 📁 Structure des Fichiers

```
├── quick_preprocessing.py      # Script principal de prétraitement
├── data_preprocessing.py       # Version complète avec classe
├── data_analysis.py           # Analyse et validation des données
├── requirements.txt           # Dépendances Python
├── run_preprocessing.ps1      # Script PowerShell automatisé
├── run_preprocessing.sh       # Script Bash automatisé
└── README_preprocessing.md    # Ce fichier
```

## 🔄 Processus de Prétraitement

### 1. Chargement du Dataset
- Téléchargement automatique depuis Hugging Face
- Dataset: `Jean-Baptiste/financial_news_sentiment_mixte_with_phrasebank_75`

### 2. Nettoyage des Données
- **Décodage HTML:** Conversion des entités HTML (`&amp;` → `&`)
- **Suppression des balises:** Élimination des tags HTML/XML
- **Normalisation des caractères:** Préservation des symboles financiers ($, €, %, etc.)
- **Espaces multiples:** Normalisation des espaces
- **Filtrage:** Suppression des textes trop courts (<10 caractères)

### 3. Tokenisation avec FinBERT
- Tokenizer: `ProsusAI/finbert`
- Longueur maximale: 512 tokens
- Padding et truncation automatiques
- Format PyTorch

### 4. Division des Données
- **Train:** 70% des données
- **Validation:** 15% des données  
- **Test:** 15% des données

## 📊 Résultats Attendus

Après exécution, vous obtiendrez :

```
financial_dataset_processed/
├── train/
├── validation/
└── test/
```

### Distribution des Labels
- **0:** Neutre (sentiment neutre)
- **1:** Positif (sentiment positif)
- **2:** Négatif (sentiment négatif)

### Statistiques Typiques
- **Taille totale:** ~15,000-20,000 échantillons
- **Longueur moyenne:** 150-200 tokens
- **Distribution équilibrée** des classes

## 🔍 Validation et Analyse

Le script `data_analysis.py` génère :

1. **Graphiques de distribution:**
   - `length_distribution_train.png`
   - `length_distribution_validation.png`
   - `length_distribution_test.png`
   - `label_distribution.png`

2. **Rapport d'analyse:**
   - Qualité des textes
   - Distribution des labels
   - Détection de problèmes
   - Échantillons aléatoires

## ⚡ Optimisations Appliquées

### Performance
- **Traitement par batch** pour accélérer le nettoyage
- **Filtrage vectorisé** avec pandas/numpy
- **Tokenisation optimisée** avec Hugging Face

### Qualité des Données
- **Préservation des symboles financiers** ($, €, %, etc.)
- **Nettoyage conservateur** pour éviter la perte d'information
- **Validation automatique** des résultats

### Mémoire
- **Suppression des colonnes inutiles** après traitement
- **Format PyTorch optimisé** pour l'entraînement
- **Sauvegarde efficace** sur disque

## 🛠️ Personnalisation

### Modifier les Paramètres

Dans `quick_preprocessing.py`, vous pouvez ajuster :

```python
# Longueur maximale des séquences
max_length = 512  # Changez selon vos besoins

# Proportions des splits
test_size = 0.3   # 30% pour validation+test
val_test_split = 0.5  # 50/50 entre validation et test

# Filtrage des textes
min_text_length = 10  # Minimum 10 caractères
```

### Ajouter des Étapes de Nettoyage

Modifiez la fonction `clean_text_fast()` :

```python
def clean_text_fast(text):
    # Vos étapes personnalisées ici
    text = custom_cleaning_step(text)
    return text
```

## 🐛 Résolution de Problèmes

### Erreur de Mémoire
```bash
# Réduire la taille des batches
export TOKENIZERS_PARALLELISM=false
python quick_preprocessing.py
```

### Dataset Non Trouvé
```bash
# Vérifier la connexion internet
# Le dataset sera téléchargé automatiquement
```

### Erreur de Tokenizer
```bash
# Réinstaller transformers
pip install --upgrade transformers
```

## 📈 Prochaines Étapes

Après le prétraitement, vous pouvez :

1. **Entraîner le modèle FinBERT**
2. **Évaluer les performances**
3. **Fine-tuner les hyperparamètres**
4. **Déployer le modèle**

## 🤝 Support

Pour toute question ou problème :
1. Vérifiez les logs d'erreur
2. Consultez la documentation Hugging Face
3. Vérifiez les versions des dépendances

---

**✅ Dataset prêt pour l'entraînement FinBERT !**
