# Étape 1: Installation des bibliothèques nécessaires (si elles ne sont pas déjà installées)
# Décommentez la ligne suivante pour l'exécuter dans un notebook
# !pip install datasets pandas matplotlib seaborn

from datasets import load_dataset
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns

# Nom du jeu de données sur la plateforme Hugging Face
dataset_name = "Jean-Baptiste/financial_news_sentiment_mixte_with_phrasebank_75"


try:
    # Étape 2: Charger le jeu de données en forçant le re-téléchargement
    # L'argument download_mode="force_redownload" résout l'erreur de cache.
    print(f"Chargement du jeu de données : {dataset_name} (en forçant le re-téléchargement)...")
    dataset = load_dataset(dataset_name, download_mode="force_redownload")
    print("Chargement terminé.")

    # Étape 3: Convertir la partition 'train' en DataFrame pandas
    # Le jeu de données original contient 'train' et 'test', nous utilisons 'train'.
    df = dataset['train'].to_pandas()

    # Étape 4: Analyse exploratoire de base
    print("\n--- Aperçu du jeu de données (5 premières lignes) ---")
    print(df.head())

    print("\n--- Informations sur le DataFrame ---")
    df.info()

    print("\n--- Distribution des sentiments ---")
    # Les étiquettes sont 0: Négatif, 1: Neutre, 2: Positif
    sentiment_counts = df['label'].value_counts().sort_index()
    sentiment_counts.index = ['Négatif (0)', 'Neutre (1)', 'Positif (2)']
    print(sentiment_counts)

    # Étape 5: Visualiser la distribution des sentiments
    print("\n--- Génération du graphique de distribution ---")
    plt.style.use('seaborn-v0_8-whitegrid')
    plt.figure(figsize=(10, 6))

    ax = sns.countplot(x='label', data=df, palette=['#D62728', '#FFBF00', '#2CA02C'], order=[0, 1, 2])

    plt.title('Distribution des Sentiments dans le Jeu de Données Financières', fontsize=16)
    plt.xlabel('Sentiment', fontsize=12)
    plt.ylabel("Nombre d'articles", fontsize=12)
    ax.set_xticklabels(['Négatif', 'Neutre', 'Positif'])

    # Ajouter le nombre exact sur chaque barre pour une meilleure lisibilité
    for p in ax.patches:
        ax.annotate(f'{p.get_height()}', (p.get_x() + p.get_width() / 2., p.get_height()),
                    ha='center', va='center', xytext=(0, 9), textcoords='offset points')

    plt.show()
    print("Graphique affiché avec succès.")

except Exception as e:
    print(f"Une erreur est survenue : {e}")

