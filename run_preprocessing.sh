#!/bin/bash
# Script bash pour automatiser le prétraitement des données financières
# Usage: ./run_preprocessing.sh

echo "🚀 DÉMARRAGE DU PRÉTRAITEMENT DES DONNÉES FINANCIÈRES"
echo "============================================================"

# Vérification de Python
echo "🔍 Vérification de l'environnement..."
if command -v python3 &> /dev/null; then
    PYTHON_CMD="python3"
    echo "✅ Python3 détecté: $(python3 --version)"
elif command -v python &> /dev/null; then
    PYTHON_CMD="python"
    echo "✅ Python détecté: $(python --version)"
else
    echo "❌ Python non trouvé. Veuillez installer Python 3.8+"
    exit 1
fi

# Vérification de pip
if command -v pip3 &> /dev/null; then
    PIP_CMD="pip3"
elif command -v pip &> /dev/null; then
    PIP_CMD="pip"
else
    echo "❌ pip non trouvé. Veuillez installer pip"
    exit 1
fi

# Installation des dépendances
echo ""
echo "📦 Installation des dépendances..."
if $PIP_CMD install -r requirements.txt; then
    echo "✅ Dépendances installées avec succès"
else
    echo "❌ Erreur lors de l'installation des dépendances"
    exit 1
fi

# Exécution du prétraitement rapide
echo ""
echo "🔄 Exécution du prétraitement..."
if $PYTHON_CMD quick_preprocessing.py; then
    echo "✅ Prétraitement terminé avec succès"
else
    echo "❌ Erreur lors du prétraitement"
    exit 1
fi

# Analyse des données (optionnel)
echo ""
read -p "❓ Voulez-vous exécuter l'analyse des données? (y/N): " analysis
if [[ $analysis == "y" || $analysis == "Y" ]]; then
    echo ""
    echo "📊 Exécution de l'analyse..."
    if $PYTHON_CMD data_analysis.py; then
        echo "✅ Analyse terminée"
    else
        echo "❌ Erreur lors de l'analyse"
    fi
fi

echo ""
echo "🎉 PROCESSUS TERMINÉ!"
echo "Les données sont prêtes pour l'entraînement du modèle."
echo "Dataset prétraité disponible dans: ./financial_dataset_processed/"
